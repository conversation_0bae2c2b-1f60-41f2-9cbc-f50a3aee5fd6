# Database connection string
POSTGRES_URL=postgresql://postgres@127.0.0.1:54320/serplens_db
# Used to encrypt JWT tokens
PAYLOAD_SECRET=YOUR_SECRET_HERE
# Used to configure CORS, format links and more. No trailing slash
NEXT_PUBLIC_SERVER_URL=https://serplens.local
# Secret used to authenticate cron jobs
CRON_SECRET=YOUR_CRON_SECRET_HERE
# Used to validate preview requests
PREVIEW_SECRET=YOUR_SECRET_HERE
#S3 BUCKET
S3_ENDPOINT=https://nyc3.digitaloceanspaces.com
S3_BUCKET=serplens-media
S3_REGION=nyc3
S3_ACCESS_KEY_ID=YOUR_KEY_HERE
S3_SECRET_ACCESS_KEY=YOUR_SECRET_HERE
#resend api key
RESEND_API_KEY=YOUR_TOKEN_HERE
FROM_NAME="SERPLENS"
FROM_ADDRESS="<EMAIL>"
#is live
NEXT_PUBLIC_IS_LIVE=0
