import { csp } from '@/utils/server'
import { withPayload } from '@payloadcms/next/withPayload'
import redirects from './redirects.js'

const NEXT_PUBLIC_SERVER_URL = process.env.VERCEL_PROJECT_PRODUCTION_URL
  ? `https://${process.env.VERCEL_PROJECT_PRODUCTION_URL}`
  : undefined || process.env.NEXT_PUBLIC_SERVER_URL || 'https://serplens.local'

const headers = async () => {
  const headers = [
    {
      source: '/(.*)',
      headers: [
        {
          key: 'Content-Security-Policy',
          value: csp
        }
      ]
    }
  ]

  return headers
}

/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      ...[NEXT_PUBLIC_SERVER_URL /* 'https://example.com' */].map((item) => {
        const url = new URL(item)

        return {
          hostname: url.hostname,
          protocol: url.protocol.replace(':', '')
        }
      })
    ]
  },
  reactStrictMode: true,
  redirects,
  headers,
  poweredByHeader: false
}

export default withPayload(nextConfig, { devBundleServerPackages: false })
