## Getting started

First, you need to install:

- Node LTS (recommend using `nvm` → `nvm install --lts && nvm use --lts`)
- [Docker](https://docs.docker.com/engine/install)
- [pnpm](https://pnpm.io/installation)
- [Homebrew](https://brew.sh)

Then, clone the repo and:

- Copy the `.env.example` to a new `.env` file
- Follow instructions to setup HTTPS

## HTTPS setup

### Generate a local SSL certificate

- `brew install mkcert`
- `brew install nss`
- `cd` to the repo
- `cd nginx/certs` (`mkdir` if not there)
- `mkcert -install`
- `mkcert -key-file serplens.local.key.pem -cert-file serplens.local.cert.pem serplens.local "*.serplens.local"`
- Two new `.pem` files should now be in the `certs` folder

### Add hosts

Run the following:

Mac

```bash
echo -e "\n\n# serplens\n127.0.0.1         serplens.local\n127.0.0.1         app.serplens.local" | sudo tee -a /etc/hosts`
```

Windows

```Powershell
Add-Content -Path "C:\Windows\System32\drivers\etc\hosts" -Value "`n`n# serplens`n127.0.0.1         serplens.local`n127.0.0.1         app.serplens.local"
```

## Running the app

You can run the application with the following commands:

- `pnpm i` – Install all dependencies
- `pnpm run dev` – Start the development server

The application will be available at:

- https://serplens.local ← Is the root

Download [PGAdmin](https://www.pgadmin.org/download/) to explore the database in a nice UI.

## Package manager

This repo uses `pnpm`. You can read more about `pnpm` [here](https://pnpm.io/). Please don't use `npm` or `yarn`, as we don't want multiple lockfile types.

## Technology

This stack is built on top of NextJS and Payload CMS. Effectively, Payload handles the database documents (known as collections) and provides a nice CMS to edit this data. NextJS handles virtually everything else (bundling, routing, back-end, front-end and much more). The front-end UI framework is React.

## Folder structure

Here is the current folder structure of the application:

```
.
├── src/                    # Main source code directory
│   ├── access/            # Payload CMS access control configurations
│   ├── app/               # NextJS app router pages and layouts
│   │   ├── (frontend)/   # Frontend pages and components
│   │   └── (payload)/    # Payload CMS admin interface
│   ├── blocks/            # Reusable content blocks
│   ├── collections/       # Payload CMS collections
│   │   ├── blocks/       # Collection-specific blocks
│   │   ├── categories/   # Category collection
│   │   ├── footer/       # Footer global configuration
│   │   ├── header/       # Header global configuration
│   │   ├── media/        # Media collection
│   │   ├── pages/        # Pages collection
│   │   ├── posts/        # Posts collection
│   │   └── users/        # Users collection
│   ├── components/        # React components
│   │   ├── ui/           # shadcn/ui components
│   │   ├── adminbar/     # Admin bar components
│   │   ├── media/        # Media-related components
│   │   └── ...          # Other reusable components
│   ├── fields/           # Payload CMS field configurations
│   ├── footer/           # Footer components
│   ├── header/           # Header components
│   │   └── nav/         # Navigation components
│   ├── heros/            # Hero section components
│   │   ├── highimpact/  # High impact hero variants
│   │   ├── lowimpact/   # Low impact hero variants
│   │   ├── mediumimpact/# Medium impact hero variants
│   │   └── posthero/    # Post-specific hero components
│   ├── hooks/            # Custom React hooks
│   ├── lib/              # Library code and configurations
│   │   ├── api/         # API client configuration
│   │   ├── api-sdk/     # Auto-generated API SDK
│   │   └── data/        # Data fetching utilities
│   ├── migrations/       # Database migration scripts
│   ├── plugins/          # Payload CMS plugins
│   │   └── hooks/       # Plugin-specific hooks
│   ├── providers/        # React context providers
│   │   ├── headertheme/ # Header theme provider
│   │   └── theme/       # Theme provider
│   ├── scripts/          # Build and utility scripts
│   ├── search/           # Search functionality
│   ├── utils/            # Utility functions
│   │   └── server/      # Server-side utilities
│   ├── constants.ts      # Application constants
│   ├── cssVariables.js   # CSS variables configuration
│   ├── environment.d.ts  # Environment type definitions
│   ├── payload-types.ts  # Auto-generated Payload types
│   └── payload.config.ts # Payload CMS configuration
├── nginx/                # Nginx configuration and SSL certificates
├── public/              # Static assets
├── .github/             # GitHub configuration
├── .vscode/             # VS Code configuration
├── .env.example         # Environment variables template
├── eslint.config.mjs    # ESLint configuration
├── .prettierrc.json     # Prettier configuration
├── components.json      # shadcn/ui component configuration
├── docker-compose.yml   # Docker Compose configuration
├── Dockerfile           # Docker configuration
├── next.config.js       # Next.js configuration
├── openapi-ts.config.js # OpenAPI TypeScript generator config
├── package.json         # Project dependencies and scripts
├── pnpm-lock.yaml       # PNPM lock file
├── postcss.config.js    # PostCSS configuration
├── startDev.js          # Development startup script
└── tsconfig.json        # TypeScript configuration
```

### Key Directory Explanations

- **`src/utils/`**: Contains utility functions organized by usage context
  - **`src/utils/server/`**: Server-side utilities that use Next.js server-only APIs (should only be imported in server components)
  - Client-side utilities are in the root `utils/` directory
- **`src/lib/`**: Library code including API clients and data fetching utilities
- **`src/collections/`**: Payload CMS collections with barrel exports for clean imports
- **`src/components/ui/`**: shadcn/ui components for consistent design system

### Configuration Files

- `docker-compose.yml`: Configures the development environment with Docker
- `next.config.js`: Next.js configuration including redirects and rewrites
- `payload.config.ts`: Payload CMS configuration including collections and plugins
- `tsconfig.json`: TypeScript configuration
- `eslint.config.mjs`: ESLint rules and configuration
- `.prettierrc.json`: Code formatting rules

### Development Tools

- [Payload access control](https://payloadcms.com/docs/access-control/overview)
- [Server actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations) and [React docs](https://react.dev/reference/rsc/server-actions)
- [Payload collections](https://payloadcms.com/docs/beta/configuration/collections)
- [Next instrumentation](https://nextjs.org/docs/app/building-your-application/optimizing/instrumentation)

## Linting and Code Quality

The codebase is linted with ESLint and formatted with Prettier. It's recommended that you use the "format on save" feature in your editor.

The linting will also happen on app start and when you try to commit to git. Please don't ever force or skip these hooks. We only ever want linted code hitting the repo.

### ESLint Configuration

Our ESLint setup includes several plugins and custom rules for enhanced code quality:

#### Core Rules

- **TypeScript**: Configured with Next.js TypeScript rules and custom overrides
- **Unused Variables**: Strict enforcement with patterns for ignored variables (prefix with `_`)
- **Import/Export**: Proper import organization and default export handling

#### eslint-plugin-unicorn

We use [eslint-plugin-unicorn](https://github.com/sindresorhus/eslint-plugin-unicorn) for modern JavaScript/TypeScript best practices:

**Filename Conventions:**

- `unicorn/filename-case`: Enforces camelCase or PascalCase for filenames
  - **PascalCase**: React components (e.g., `InputOtp.tsx`, `HeaderClient.tsx`)
  - **camelCase**: Utilities, configs, and other files (e.g., `canUseDom.ts`, `generateMeta.ts`)

**Abbreviation Prevention:**

- `unicorn/prevent-abbreviations`: Prevents unclear abbreviations with an extensive allowList:
  - **Allowed**: `params`, `env`, `dev`, `prod`, `props`, `ref`, `doc`, `docs`, `req`, `args`, `err`, `val`, `prev`, `src`, `res`
  - **Case-sensitive**: Both lowercase and capitalized versions are allowed (e.g., `Props`, `Params`)

**Import Style Enforcement:**

- `unicorn/import-style`: Enforces consistent import patterns
  - **Path imports**: Must use named imports (`import { join } from 'path'`)
  - **Util imports**: Disabled to allow flexible utility imports

**Disabled Rules:**

- `unicorn/no-null`: Allows `null` usage (disabled)
- `unicorn/no-array-reduce`: Allows `Array.reduce()` usage (disabled)
- `unicorn/prefer-top-level-await`: Allows non-top-level await (disabled)
- `unicorn/prefer-global-this`: Allows `window`/`global` over `globalThis` (disabled)

#### File-Specific Overrides

- **App Router Files**: Relaxed rules for Next.js app directory and config files
- **Generated Files**: Ignored patterns include migrations, payload-types, and auto-generated API SDK
- **Payload Admin**: Admin interface files are excluded from linting

### Prettier Configuration

Code formatting is handled by Prettier with consistent rules across the project. The configuration ensures uniform code style for better readability and maintainability.

Core features:

- [Pre-configured Payload Config](#how-it-works)
- [Authentication](#users-authentication)
- [Access Control](#access-control)
- [Layout Builder](#layout-builder)
- [Draft Preview](#draft-preview)
- [Live Preview](#live-preview)
- [On-demand Revalidation](#on-demand-revalidation)
- [SEO](#seo)
- [Search](#search)
- [Redirects](#redirects)
- [Jobs and Scheduled Publishing](#jobs-and-scheduled-publish)
- [Website](#website)

## How it works

The Payload config is tailored specifically to the needs of most websites. It is pre-configured in the following ways:

### Collections

See the [Collections](https://payloadcms.com/docs/configuration/collections) docs for details on how to extend this functionality.

- #### Users (Authentication)

  Users are auth-enabled collections that have access to the admin panel and unpublished content. See [Access Control](#access-control) for more details.

  For additional help, see the official [Auth Example](https://github.com/payloadcms/payload/tree/main/examples/auth) or the [Authentication](https://payloadcms.com/docs/authentication/overview#authentication-overview) docs.

- #### Posts

  Posts are used to generate blog posts, news articles, or any other type of content that is published over time. All posts are layout builder enabled so you can generate unique layouts for each post using layout-building blocks, see [Layout Builder](#layout-builder) for more details. Posts are also draft-enabled so you can preview them before publishing them to your website, see [Draft Preview](#draft-preview) for more details.

- #### Pages

  All pages are layout builder enabled so you can generate unique layouts for each page using layout-building blocks, see [Layout Builder](#layout-builder) for more details. Pages are also draft-enabled so you can preview them before publishing them to your website, see [Draft Preview](#draft-preview) for more details.

- #### Media

  This is the uploads enabled collection used by pages, posts, and projects to contain media like images, videos, downloads, and other assets. It features pre-configured sizes, focal point and manual resizing to help you manage your pictures.

- #### Categories

  A taxonomy used to group posts together. Categories can be nested inside of one another, for example "News > Technology". See the official [Payload Nested Docs Plugin](https://payloadcms.com/docs/plugins/nested-docs) for more details.

### Globals

See the [Globals](https://payloadcms.com/docs/configuration/globals) docs for details on how to extend this functionality.

- `Header`

  The data required by the header on your front-end like nav links.

- `Footer`

  Same as above but for the footer of your site.

## Access control

Basic access control is setup to limit access to various content based based on publishing status.

- `users`: Users can access the admin panel and create or edit content.
- `posts`: Everyone can access published posts, but only users can create, update, or delete them.
- `pages`: Everyone can access published pages, but only users can create, update, or delete them.

For more details on how to extend this functionality, see the [Payload Access Control](https://payloadcms.com/docs/access-control/overview#access-control) docs.

## Layout Builder

Create unique page layouts for any type of content using a powerful layout builder. This template comes pre-configured with the following layout building blocks:

- Hero
- Content
- Media
- Call To Action
- Archive

Each block is fully designed and built into the front-end website that comes with this template. See [Website](#website) for more details.

## Lexical editor

A deep editorial experience that allows complete freedom to focus just on writing content without breaking out of the flow with support for Payload blocks, media, links and other features provided out of the box. See [Lexical](https://payloadcms.com/docs/rich-text/overview) docs.

## Draft Preview

All posts and pages are draft-enabled so you can preview them before publishing them to your website. To do this, these collections use [Versions](https://payloadcms.com/docs/configuration/collections#versions) with `drafts` set to `true`. This means that when you create a new post, project, or page, it will be saved as a draft and will not be visible on your website until you publish it. This also means that you can preview your draft before publishing it to your website. To do this, we automatically format a custom URL which redirects to your front-end to securely fetch the draft version of your content.

Since the front-end of this template is statically generated, this also means that pages, posts, and projects will need to be regenerated as changes are made to published documents. To do this, we use an `afterChange` hook to regenerate the front-end when a document has changed and its `_status` is `published`.

For more details on how to extend this functionality, see the official [Draft Preview Example](https://github.com/payloadcms/payload/tree/examples/draft-preview).

## Live preview

In addition to draft previews you can also enable live preview to view your end resulting page as you're editing content with full support for SSR rendering. See [Live preview docs](https://payloadcms.com/docs/live-preview/overview) for more details.

## On-demand Revalidation

We've added hooks to collections and globals so that all of your pages, posts, or footer or header, change they will automatically be updated in the frontend via on-demand revalidation supported by Nextjs.

> Note: if an image has been changed, for example it's been cropped, you will need to republish the page it's used on in order to be able to revalidate the Nextjs image cache.

## SEO

This template comes pre-configured with the official [Payload SEO Plugin](https://payloadcms.com/docs/plugins/seo) for complete SEO control from the admin panel. All SEO data is fully integrated into the front-end website that comes with this template. See [Website](#website) for more details.

## Search

This template also pre-configured with the official [Payload Search Plugin](https://payloadcms.com/docs/plugins/search) to showcase how SSR search features can easily be implemented into Next.js with Payload. See [Website](#website) for more details.

## Redirects

If you are migrating an existing site or moving content to a new URL, you can use the `redirects` collection to create a proper redirect from old URLs to new ones. This will ensure that proper request status codes are returned to search engines and that your users are not left with a broken link. This template comes pre-configured with the official [Payload Redirects Plugin](https://payloadcms.com/docs/plugins/redirects) for complete redirect control from the admin panel. All redirects are fully integrated into the front-end website that comes with this template. See [Website](#website) for more details.

## Jobs and Scheduled Publish

We have configured [Scheduled Publish](https://payloadcms.com/docs/versions/drafts#scheduled-publish) which uses the [jobs queue](https://payloadcms.com/docs/jobs-queue/jobs) in order to publish or unpublish your content on a scheduled time. The tasks are run on a cron schedule and can also be run as a separate instance if needed.

> Note: When deployed on Vercel, depending on the plan tier, you may be limited to daily cron only.

## Website

This includes a beautifully designed, production-ready front-end built with the [Next.js App Router](https://nextjs.org), served right alongside your Payload app in a instance. This makes it so that you can deploy both your backend and website where you need it.

Core features:

- [Next.js App Router](https://nextjs.org)
- [TypeScript](https://www.typescriptlang.org)
- [React Hook Form](https://react-hook-form.com)
- [Payload Admin Bar](https://github.com/payloadcms/payload/tree/main/packages/admin-bar)
- [TailwindCSS styling](https://tailwindcss.com/)
- [shadcn/ui components](https://ui.shadcn.com/)
- User Accounts and Authentication
- Fully featured blog
- Publication workflow
- Dark mode
- Pre-made layout building blocks
- SEO
- Search
- Redirects
- Live preview

### Cache

Although Next.js includes a robust set of caching strategies out of the box, Payload Cloud proxies and caches all files through Cloudflare using the [Official Cloud Plugin](https://www.npmjs.com/package/@payloadcms/payload-cloud). This means that Next.js caching is not needed and is disabled by default. If you are hosting your app outside of Payload Cloud, you can easily reenable the Next.js caching mechanisms by removing the `no-store` directive from all fetch requests in `./src/app/_api` and then removing all instances of `export const dynamic = 'force-dynamic'` from pages files, such as `./src/app/(pages)/[slug]/page.tsx`. For more details, see the official [Next.js Caching Docs](https://nextjs.org/docs/app/building-your-application/caching).

#### Migrations

[Migrations](https://payloadcms.com/docs/database/migrations) are essentially SQL code versions that keeps track of your schema. When deploy with Postgres you will need to make sure you create and then run your migrations.

Locally create a migration

```bash
pnpm payload migrate:create
```

This creates the migration files you will need to push alongside with your new configuration.

On the server after building and before running `pnpm start` you will want to run your migrations

```bash
pnpm payload migrate
```

This command will check for any migrations that have not yet been run and try to run them and it will keep a record of migrations that have been run in the database.
