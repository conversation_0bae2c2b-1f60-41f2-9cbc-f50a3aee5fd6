import React from 'react'
import { Width } from '@/blocks/form/width'
import { RichText } from '@/components/RichText'
import { SerializedEditorState } from '@payloadcms/richtext-lexical/lexical'

export const Message: React.FC<{ message: SerializedEditorState }> = ({ message }) => {
  return (
    <Width className='my-12' width='100'>
      {message && <RichText data={message} />}
    </Width>
  )
}
