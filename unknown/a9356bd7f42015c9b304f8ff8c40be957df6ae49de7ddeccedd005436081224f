import { ElementType, ReactNode } from 'react'
import { type VariantProps, cva } from 'class-variance-authority'
import { cn } from '@/utils'

export const textVariants = cva('text-foreground', {
  variants: {
    variant: {
      xs: 'text-xs',
      sm: 'text-sm',
      base: 'text-sm md:text-base',
      lg: 'text-lg',
      xl: 'text-base md:text-xl',
      xxl: 'text-xl md:text-xxl',
      '2xl': 'text-xl md:text-2xl',
      '3xl': 'text-2xl md:text-3xl',
      '4xl': 'text-2xl md:text-4xl',
      '5xl': 'text-4xl md:text-5xl',
      '6xl': 'text-4xl md:text-6xl tracking-3',
      '7xl': 'text-5xl md:text-7xl tracking-3'
    },
    weight: {
      medium: 'font-medium',
      normal: 'font-normal',
      semibold: 'font-semibold',
      bold: 'font-bold'
    }
  },
  defaultVariants: {
    variant: 'sm',
    weight: 'normal'
  }
})

interface TextProps extends VariantProps<typeof textVariants> {
  as?: ElementType
  className?: string
  children: ReactNode
}

export const Text = ({
  as: Component = 'p',
  className,
  variant,
  weight,
  children,
  ...props
}: TextProps) => {
  return (
    <Component className={cn(textVariants({ variant, weight }), className)} {...props}>
      {children}
    </Component>
  )
}
