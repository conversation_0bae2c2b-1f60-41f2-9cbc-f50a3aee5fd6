import camelCase from 'camelcase'
import fs from 'node:fs'
import type { CollectionConfig } from 'payload'
import configPromise from '@/payload.config'

const ignoreList = new Set([
  'payload-locked-documents',
  'payload-preferences',
  'payload-migrations',
  'payload-jobs',
  'redirects',
  'forms',
  'form-submissions',
  'search'
])

// Define OpenAPI path item and document types
type OpenAPIPathItem = {
  get?: OpenAPIOperation
  post?: OpenAPIOperation
  patch?: OpenAPIOperation
  delete?: OpenAPIOperation
  [key: string]: OpenAPIOperation | undefined
}

type OpenAPIOperation = {
  summary: string
  operationId: string
  tags: string[]
  parameters?: { name: string; in: string; required: boolean; schema: { type: string } }[]
  requestBody?: {
    content: {
      'application/json'?: {
        schema: {
          type: string
          properties?: Record<string, unknown>
        }
      }
      'multipart/form-data'?: {
        schema: {
          type: string
          properties: Record<string, unknown>
          required?: string[]
        }
      }
    }
  }
  responses: Record<
    string,
    {
      description: string
      content?: {
        'application/json': {
          schema: {
            type: string
            properties?: Record<string, unknown>
          }
        }
      }
    }
  >
}

// OpenAPI base document
const openApiDocument: {
  openapi: string
  info: {
    title: string
    version: string
    description: string
  }
  paths: Record<string, OpenAPIPathItem>
} = {
  openapi: '3.0.0',
  info: {
    title: 'SERPlens API',
    version: '1.0.0',
    description: 'Auto-generated OpenAPI document for the SERPlens API.'
  },
  paths: {}
}

const processOperationId = (name: string) => {
  return camelCase(name)
}

// Function to convert Payload collections to OpenAPI paths
const convertCollectionToOpenApi = (collection: CollectionConfig) => {
  const { slug, labels, upload } = collection
  // Handle potentially undefined labels with defaults
  const pluralSentence = typeof labels?.plural === 'string' ? labels.plural : slug
  const singularSentence = typeof labels?.singular === 'string' ? labels.singular : slug

  const plural = pluralSentence.toLowerCase()
  const singular = singularSentence.toLowerCase()

  const path = `/api/${slug}`

  // Add path to openApiDocument.paths
  if (!openApiDocument.paths[path]) {
    openApiDocument.paths[path] = {}
  }

  // Find
  openApiDocument.paths[path].get = {
    summary: `List all ${plural}`,
    operationId: processOperationId(`find-${plural}`),
    tags: [slug],
    responses: {
      200: {
        description: `A list of ${plural}`,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                docs: {
                  type: 'array'
                  // TODO: Sort this!
                  // items: { type: 'object', properties: generateProperties(fields) }
                },
                totalDocs: { type: 'integer' },
                limit: { type: 'integer' },
                totalPages: { type: 'integer' },
                page: { type: 'integer' },
                pagingCounter: { type: 'integer' },
                hasPrevPage: { type: 'boolean' },
                hasNextPage: { type: 'boolean' },
                prevPage: { type: 'integer', nullable: true },
                nextPage: { type: 'integer', nullable: true }
              }
            }
          }
        }
      }
    }
  }

  // Create
  openApiDocument.paths[path].post = {
    summary: `Create a new ${singular}`,
    operationId: processOperationId(`create-${singular}`),
    tags: [slug],
    requestBody: {
      content: upload
        ? {
            'multipart/form-data': {
              schema: {
                type: 'object',
                properties: {
                  file: {
                    type: 'string',
                    format: 'binary',
                    description: 'The file to upload'
                  },
                  _payload: {
                    type: 'string',
                    description: 'JSON-stringified object containing collection fields'
                  }
                },
                required: ['file']
              }
            }
          }
        : {
            'application/json': {
              schema: {
                type: 'object'
                // TODO: Sort this!
                // properties: generateProperties(fields)
              }
            }
          }
    },
    responses: {
      201: {
        description: `Created ${singular}`,
        content: {
          'application/json': {
            schema: {
              type: 'object'
              // TODO: Sort this!
              // properties: generateProperties(fields)
            }
          }
        }
      }
    }
  }

  // Update
  openApiDocument.paths[path].patch = {
    summary: `Update ${plural}`,
    operationId: processOperationId(`update-${plural}`),
    tags: [slug],
    requestBody: {
      content: upload
        ? {
            'multipart/form-data': {
              schema: {
                type: 'object',
                properties: {
                  file: {
                    type: 'string',
                    format: 'binary',
                    description: 'The file to upload (optional for updates)'
                  },
                  _payload: {
                    type: 'string',
                    description: 'JSON-stringified object containing collection fields'
                  }
                }
              }
            }
          }
        : {
            'application/json': {
              schema: {
                type: 'object'
                // TODO: Sort this!
                // properties: generateProperties(fields)
              }
            }
          }
    },
    responses: {
      200: {
        description: `Updated ${plural}`,
        content: {
          'application/json': {
            schema: {
              type: 'object'
              // TODO: Sort this!
              // properties: generateProperties(fields)
            }
          }
        }
      }
    }
  }

  // Delete
  openApiDocument.paths[path].delete = {
    summary: `Delete ${plural}`,
    operationId: processOperationId(`delete-${plural}`),
    tags: [slug],
    responses: {
      204: {
        description: `${plural} deleted successfully`
      }
    }
  }

  // Find By ID, Update By ID, Delete By ID
  const pathWithId = `${path}/{id}`
  openApiDocument.paths[pathWithId] = {}

  // Get by ID
  openApiDocument.paths[pathWithId].get = {
    summary: `Retrieve a ${singular} by ID`,
    operationId: processOperationId(`find-${singular}-by-id`),
    tags: [slug],
    parameters: [{ name: 'id', in: 'path', required: true, schema: { type: 'string' } }],
    responses: {
      200: {
        description: `A single ${singular}`,
        content: {
          'application/json': {
            schema: {
              type: 'object'
              // TODO: Sort this!
              // properties: generateProperties(fields)
            }
          }
        }
      }
    }
  }

  // Update by ID
  openApiDocument.paths[pathWithId].patch = {
    summary: `Update a ${singular} by ID`,
    operationId: processOperationId(`update-${singular}-by-id`),
    tags: [slug],
    parameters: [{ name: 'id', in: 'path', required: true, schema: { type: 'string' } }],
    requestBody: {
      content: upload
        ? {
            'multipart/form-data': {
              schema: {
                type: 'object',
                properties: {
                  file: {
                    type: 'string',
                    format: 'binary',
                    description: 'The file to upload (optional for updates)'
                  },
                  _payload: {
                    type: 'string',
                    description: 'JSON-stringified object containing collection fields'
                  }
                }
              }
            }
          }
        : {
            'application/json': {
              schema: {
                type: 'object'
                // TODO: Sort this!
                // properties: generateProperties(fields)
              }
            }
          }
    },
    responses: {
      200: {
        description: `Updated ${singular}`,
        content: {
          'application/json': {
            schema: {
              type: 'object'
              // TODO: Sort this!
              // properties: generateProperties(fields)
            }
          }
        }
      }
    }
  }

  // Delete by ID
  openApiDocument.paths[pathWithId].delete = {
    summary: `Delete a ${singular} by ID`,
    operationId: processOperationId(`delete-${singular}-by-id`),
    tags: [slug],
    parameters: [{ name: 'id', in: 'path', required: true, schema: { type: 'string' } }],
    responses: {
      204: {
        description: `${singular} deleted`
      }
    }
  }

  // Count path
  const countPath = `${path}/count`
  openApiDocument.paths[countPath] = {}
  openApiDocument.paths[countPath].get = {
    summary: `Count of ${plural}`,
    operationId: processOperationId(`count-${plural}`),
    tags: [slug],
    responses: {
      200: {
        description: `Count of ${plural}`,
        content: {
          'application/json': {
            schema: { type: 'object', properties: { totalDocs: { type: 'integer' } } }
          }
        }
      }
    }
  }

  if (collection.auth) {
    // Auth paths
    // Login path
    openApiDocument.paths[`${path}/login`] = {
      post: {
        summary: 'Login',
        operationId: processOperationId('login'),
        tags: ['auth'],
        responses: {
          200: {
            description: 'Login',
            content: {
              'application/json': {
                schema: { type: 'object' }
              }
            }
          }
        }
      }
    }

    // Logout path
    openApiDocument.paths[`${path}/logout`] = {
      post: {
        summary: 'Logout',
        operationId: processOperationId('logout'),
        tags: ['auth'],
        responses: {
          200: {
            description: 'Logout',
            content: {
              'application/json': {
                schema: { type: 'object' }
              }
            }
          }
        }
      }
    }

    // Unlock path
    openApiDocument.paths[`${path}/unlock`] = {
      post: {
        summary: 'Unlock',
        operationId: processOperationId('unlock'),
        tags: ['auth'],
        requestBody: {
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  email: { type: 'string' }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: 'Unlock',
            content: {
              'application/json': {
                schema: { type: 'object' }
              }
            }
          }
        }
      }
    }

    // Refresh token path
    openApiDocument.paths[`${path}/refresh-token`] = {
      post: {
        summary: 'Refresh token',
        operationId: processOperationId('refresh-token'),
        tags: ['auth'],
        responses: {
          200: {
            description: 'Refresh token',
            content: {
              'application/json': {
                schema: { type: 'object' }
              }
            }
          }
        }
      }
    }

    // Current user path
    openApiDocument.paths[`${path}/me`] = {
      get: {
        summary: 'Current user',
        operationId: processOperationId('current-user'),
        tags: ['auth'],
        responses: {
          200: {
            description: 'Current user',
            content: {
              'application/json': {
                schema: { type: 'object' }
              }
            }
          }
        }
      }
    }

    // Forgot password path
    openApiDocument.paths[`${path}/forgot-password`] = {
      post: {
        summary: 'Forgot password',
        operationId: processOperationId('forgot-password'),
        tags: ['auth'],
        requestBody: {
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  email: { type: 'string' }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: 'Forgot password',
            content: {
              'application/json': {
                schema: { type: 'object' }
              }
            }
          }
        }
      }
    }

    // Reset password path
    openApiDocument.paths[`${path}/reset-password`] = {
      post: {
        summary: 'Reset password',
        operationId: processOperationId('reset-password'),
        tags: ['auth'],
        requestBody: {
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  token: { type: 'string' },
                  password: { type: 'string' }
                }
              }
            }
          }
        },
        responses: {
          200: {
            description: 'Reset password',
            content: {
              'application/json': {
                schema: { type: 'object' }
              }
            }
          }
        }
      }
    }

    // Verify token path
    openApiDocument.paths[`${path}/verify/{token}`] = {
      post: {
        summary: 'Verify token',
        operationId: processOperationId('verify-token'),
        tags: ['auth'],
        responses: {
          200: {
            description: 'Verify token',
            content: {
              'application/json': {
                schema: { type: 'object' }
              }
            }
          }
        }
      }
    }
  }
}

// Function to generate auth-related paths (no longer used, keeping for reference)
// const generateAuthPath = ({
//   summary,
//   method,
//   requestBody = null
// }: {
//   summary: string
//   method: string
//   requestBody?: { [key: string]: string } | null
// }): OpenAPIPathItem => {
//   const methodKey = method.toLowerCase()
//   const operation: OpenAPIOperation = {
//     summary,
//     operationId: processOperationId(summary),
//     tags: ['auth'],
//     responses: {
//       200: {
//         description: summary,
//         content: {
//           'application/json': {
//             schema: { type: 'object' }
//           }
//         }
//       }
//     }
//   }

//   // If request body is needed, add to the operation
//   if (requestBody) {
//     operation.requestBody = {
//       content: {
//         'application/json': {
//           schema: {
//             type: 'object',
//             properties: requestBody
//           }
//         }
//       }
//     }
//   }

//   // Return the path item with the operation at the specified method
//   return { [methodKey]: operation }
// }

// const generateProperties = (fields) => {
//   const properties = {}

//   fields.forEach((field) => {
//     const fieldType = field.type
//     let openApiType

//     switch (fieldType) {
//       case 'text':
//       case 'title':
//       case 'slug':
//       case 'richText':
//       case 'textarea':
//       case 'code':
//         openApiType = 'string'
//         break

//       case 'boolean': // Checkbox, Radio group, and UI fields are booleans
//       case 'checkbox':
//       case 'radio':
//         openApiType = 'boolean'
//         break

//       case 'number':
//       case 'integer':
//       case 'point':
//         openApiType = 'integer'
//         break

//       case 'email':
//         openApiType = 'string'
//         properties[field.name].format = 'email'
//         break

//       case 'date':
//         openApiType = 'string'
//         properties[field.name].format = 'date'
//         break

//       case 'datetime':
//         openApiType = 'string'
//         properties[field.name].format = 'date-time'
//         break

//       case 'array':
//       case 'select':
//         openApiType = 'array'
//         properties[field.name].items = generateProperties(field.options) // Recursively handle array fields
//         break

//       case 'file':
//       case 'upload':
//         openApiType = 'string'
//         properties[field.name].format = 'uri'
//         break

//       case 'group':
//         openApiType = 'object'
//         properties[field.name].properties = generateProperties(field.fields) // Recursively handle nested groups
//         break

//       case 'relationship':
//         openApiType = 'object'
//         properties[field.name].properties = {
//           // Relationship can link to another collection (so it's an object with the related fields)
//           id: { type: 'string' } // Assuming a related item with a string ID
//         }
//         break

//       case 'join':
//         openApiType = 'array'
//         properties[field.name].items = {
//           type: 'object',
//           properties: {
//             id: { type: 'string' }
//           }
//         }
//         break

//       case 'tabs':
//         openApiType = 'array'
//         properties[field.name].items = { type: 'string' } // Tabs are usually just strings representing tab names
//         break

//       case 'row':
//         openApiType = 'array'
//         properties[field.name].items = { type: 'object' } // Row can be an array of objects (fields inside the row)
//         break

//       case 'collapsible':
//         openApiType = 'object'
//         properties[field.name].properties = generateProperties(field.fields) // Collapsible groups can have nested properties
//         break

//       default:
//         openApiType = 'string' // Default to string if no match
//         break
//     }

//     // Add the field to properties
//     properties[field.name] = {
//       type: openApiType,
//       ...(properties[field.name] || {})
//     }
//   })

//   return properties
// }

// Function to generate OpenAPI properties from fields
// const generateProperties = (fields) => {
//   const properties = {}

//   fields.forEach((field) => {
//     properties[field.name] = {
//       type: field.type === 'text' ? 'string' : field.type
//     }
//   })

//   return properties
// }

export const generateApi = async () => {
  const config = await configPromise

  const collections = config.collections.filter((c) => !ignoreList.has(c.slug))

  // Generate paths for each Payload collection
  for (const collection of collections) {
    convertCollectionToOpenApi(collection)
  }

  // Write OpenAPI document to JSON file
  fs.writeFileSync('openapi.json', JSON.stringify(openApiDocument, null, 2))
  console.log('OpenAPI document generated: openapi.json')
}
