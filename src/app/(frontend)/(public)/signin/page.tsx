import { HeaderWrapper } from '@/components/HeaderWrapper'
import { SocialButton } from '@/components/SocialButton'
import { Text } from '@/components/ui'
import { LoginForm } from './SignInForm'

export default function Page() {
  return (
    <>
      <HeaderWrapper
        title='Welcome to SERP Lens'
        description="Don't have an account?"
        link='./signup'
        linkText='Sign Up.'
      />
      <div className='space-y-4 md:space-y-6'>
        <SocialButton platform='Google' text='Sign In with Google' />
        <SocialButton platform='Apple' text='Sign In with Apple' />
      </div>
      <div className='flex items-center my-5 sm:my-6'>
        <div className='flex-grow border-t'></div>
        <Text
          variant='xs'
          weight='medium'
          className='mx-4 text-foreground text-center leading-5'>
          OR
        </Text>
        <div className='flex-grow border-t'></div>
      </div>
      <LoginForm />
    </>
  )
}
