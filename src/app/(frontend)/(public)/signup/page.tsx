import Link from 'next/link'
import { Header<PERSON>rap<PERSON> } from '@/components/HeaderWrapper'
import { SocialButton } from '@/components/SocialButton'
import { Text } from '@/components/ui'
import { SignUpForm } from './SignUpForm'

export default function Page() {
  return (
    <>
      <HeaderWrapper
        title='Create Account'
        description='Already have an account?'
        link='./signin'
        linkText='Login.'
      />
      <div className='space-y-4 md:space-y-6'>
        <SocialButton platform='Google' text='Sign Up with Google' />
        <SocialButton platform='Apple' text='Sign Up with Apple' />
      </div>
      <div className='flex items-center my-5 sm:my-6'>
        <div className='flex-grow border-t'></div>
        <Text
          variant='xs'
          weight='medium'
          className='mx-4 text-foreground text-center leading-5'>
          OR
        </Text>
        <div className='flex-grow border-t'></div>
      </div>
      <SignUpForm />
      <Text as='p' weight='medium' className='leading-6 mt-5'>
        By creating an account, you agree to our{' '}
        <Link href='/terms' className='text-primary hover:underline'>
          terms
        </Link>{' '}
        and{' '}
        <Link href='/privacy' className='text-primary hover:underline'>
          privacy policy.
        </Link>
      </Text>
    </>
  )
}
