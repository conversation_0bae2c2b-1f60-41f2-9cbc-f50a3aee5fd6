@import 'tailwindcss';

@import 'tw-animate-css';
@plugin "@tailwindcss/typography";

@custom-variant dark (&:is(.dark *));
/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@theme {
  --max-width-486: 486px;
  --max-width-768: 768px;

  --text-xxl: 28px;
  --text-2xl: 32px;
  --text-5xl: 42px;
  --text-6xl: 64px;
  --text-7xl: 80px;

  --tracking-3: -3.5px;
  --gap-22: 87px;

  --gradient-text: linear-gradient(180deg, #ffffff -22.18%, #999999 125.65%);
  --border-gradient: linear-gradient(
    162.43deg,
    rgba(255, 255, 255, 0.29) -206.86%,
    rgba(102, 102, 102, 0.29) 88.5%
  );
  --gradient-card: linear-gradient(
    180deg,
    rgba(66, 118, 95, 0) 0%,
    rgba(66, 118, 95, 0.3) 100%
  );

  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);

  --color-background: var(--background);

  --color-blurred: var(--blurred);
  --color-blurred-light: var(--blurred-muted-light);

  --color-button: var(--button);
  --color-border: var(--border);

  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);

  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);

  --color-foreground: var(--foreground);
  --color-input: var(--input);

  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);

  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  --color-light: var(--light);
  --color-light-foreground: var(--light-foreground);

  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);

  --color-ring: var(--ring);
  --color-custom-offset: var(--ring-offset);

  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);

  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);
  --color-warning-secondary: var(--warning-secondary);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }

  @keyframes scroll {
    0% {
      transform: translateX(0%);
    }

    100% {
      transform: translateX(-50%);
    }
  }
}

:root {
  --radius: 0.625rem;
  --background: #051610;
  --foreground: #fffbf9;

  --card: linear-gradient(180deg, rgba(66, 118, 95, 0) 0%, rgba(66, 118, 95, 0.3) 100%);
  --card-foreground: oklch(0.145 0 0);

  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);

  --primary: #74b16e;
  --primary-foreground: #ddeaf805;

  --secondary: #42765f;
  --secondary-foreground: #213027;

  --muted: #d2eccf;
  --muted-foreground: #cbd5e1;

  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);

  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: 210 40% 98%;

  --border: #d6ebfd30;
  --input: #cbd5e1;
  --ring: oklch(0.708 0 0);

  --success: 196 52% 74%;
  --warning: 34 89% 85%;
  --error: 10 100% 86%;
}

.dark {
  --background: 0 0% 0%;
  --foreground: 210 40% 98%;

  --card: 0 0% 4%;
  --card-foreground: 210 40% 98%;

  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;

  --primary: 210 40% 98%;
  --primary-foreground: 222.2 47.4% 11.2%;

  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;

  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;

  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;

  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;

  --border: 0, 0%, 15%, 0.8;
  --input: 217.2 32.6% 17.5%;
  --ring: 212.7 26.8% 83.9%;

  --success: 196 100% 14%;
  --warning: 34 51% 25%;
  --error: 10 39% 43%;
}

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    font-family: 'Inter';
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    @apply bg-background text-foreground;
  }

  .text-gradient {
    background: var(--gradient-text);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .bg-card-gradient {
    background: var(--gradient-card);
  }

  .box {
    border: 2px solid transparent;
    border-image-source: var(--border-gradient);
    border-image-slice: 1;
  }

  .animate-scroll {
    animation: scroll 30s linear infinite;
  }

  .scroll-normal {
    animation-direction: normal;
  }

  .scroll-reverse {
    animation-direction: reverse;
  }
}
