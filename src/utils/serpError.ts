export class SerpError extends Error {
  digest: string

  constructor({ digest, message = '' }: { digest: string; message?: string }) {
    super(message)
    this.digest = digest
    this.name = 'SerpError'

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, SerpError)
    }
  }

  toJSON() {
    return {
      message: this.message,
      digest: this.digest,
      name: this.name
    }
  }
}
