import { getServerSideURL } from './getServerSideUrl'

const url = getServerSideURL()
const doEndpoint = process.env.S3_ENDPOINT

const policies = {
  'default-src': ["'self'", url],
  'script-src': [
    "'self'",
    url,
    "'unsafe-inline'",
    "'unsafe-eval'",
    'https://maps.googleapis.com',
    'https://vercel.live'
  ],
  'child-src': ["'self'", url],
  'style-src': ["'self'", url, "'unsafe-inline'", 'https://fonts.googleapis.com'],
  'img-src': [
    "'self'",
    url,
    'https://raw.githubusercontent.com',
    doEndpoint,
    'blob:',
    'data:'
  ],
  'font-src': ["'self'", url, 'https://fonts.gstatic.com'],
  'frame-src': ["'self'", url, 'https://vercel.live'],
  'connect-src': ["'self'", url, 'https://maps.googleapis.com']
}

export const csp = Object.entries(policies)
  .map(([key, value]) => {
    if (Array.isArray(value)) {
      return `${key} ${value.join(' ')}`
    }
    return ''
  })
  .join('; ')
