import type { Block } from 'payload'
import { collections } from '@/constants'

export const code: Block = {
  slug: collections.blocks.code.slug,
  interfaceName: 'CodeBlock',
  fields: [
    {
      name: 'language',
      type: 'select',
      defaultValue: 'typescript',
      options: [
        {
          label: 'Typescript',
          value: 'typescript'
        },
        {
          label: 'Javascript',
          value: 'javascript'
        },
        {
          label: 'CSS',
          value: 'css'
        }
      ]
    },
    {
      name: 'code',
      type: 'code',
      label: false,
      required: true
    }
  ]
}
