import type { CollectionConfig } from 'payload'
import { authenticated } from '@/access'
import { collections } from '@/constants'

export const users: CollectionConfig = {
  slug: collections.users.slug,
  access: {
    admin: authenticated,
    create: authenticated,
    delete: authenticated,
    read: authenticated,
    update: authenticated
  },
  admin: {
    defaultColumns: ['name', 'email'],
    useAsTitle: 'name'
  },
  auth: true,
  fields: [
    {
      name: 'name',
      type: 'text'
    },
    {
      name: 'roles',
      type: 'select',
      options: [
        { label: 'Subscriber', value: collections.users.roles.subscriber },
        { label: 'Editor', value: collections.users.roles.editor },
        { label: 'Admin', value: collections.users.roles.admin },
        { label: 'Super Admin', value: collections.users.roles.superAdmin }
      ],
      hasMany: true,
      required: true,
      defaultValue: collections.users.roles.subscriber
    }
  ],
  timestamps: true
}
