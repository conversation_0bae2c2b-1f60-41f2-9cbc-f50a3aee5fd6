import type { CollectionConfig } from 'payload'
import { anyone, authenticated } from '@/access'
import { collections } from '@/constants'
import { slugField } from '@/fields/slug'

export const categories: CollectionConfig = {
  slug: collections.categories.slug,
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated
  },
  admin: {
    useAsTitle: 'title'
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true
    },
    ...slugField()
  ]
}
