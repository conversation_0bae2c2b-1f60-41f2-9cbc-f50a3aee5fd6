import type { GlobalConfig } from 'payload'
import { collections } from '@/constants'
import { link } from '@/fields'
import { revalidateFooter } from './hooks'

export const footer: GlobalConfig = {
  slug: collections.footer.slug,
  access: {
    read: () => true
  },
  fields: [
    {
      name: 'navItems',
      type: 'array',
      fields: [
        link({
          appearances: false
        })
      ],
      maxRows: 6,
      admin: {
        initCollapsed: true,
        components: {
          RowLabel: '@/footer/RowLabel#RowLabel'
        }
      }
    }
  ],
  hooks: {
    afterChange: [revalidateFooter]
  }
}
