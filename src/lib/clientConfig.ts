/**
 * API Client Runtime Configuration
 *
 * This file configures the Next.js API client using the Runtime API pattern.
 * It sets up base URLs, default headers, and other client configuration
 * that gets applied before the client is initialized.
 *
 * @see https://heyapi.dev/openapi-ts/clients/next-js#runtime-api
 */
import { getClientSideURL } from '@/utils'
import type { CreateClientConfig } from './api-sdk/client.gen'

// Server-side URL function (inline to avoid import issues)
const getServerSideURL = () => {
  let url = process.env.NEXT_PUBLIC_SERVER_URL

  if (!url && process.env.VERCEL_PROJECT_PRODUCTION_URL) {
    return `https://${process.env.VERCEL_PROJECT_PRODUCTION_URL}`
  }

  if (!url) {
    url = 'https://serplens.local'
  }
  return url
}

export const createClientConfig: CreateClientConfig = (config) => {
  const isServer = typeof window === 'undefined'

  // Configure base URL for both server and client
  const baseUrl = isServer ? getServerSideURL() : getClientSideURL()

  return {
    ...config,
    baseUrl,
    headers: {
      'x-serplens': 'true',
      ...(config?.headers || {})
    }
  }
}
