export const allowedImageFileTypes = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'image/jpg'
]
export const allowedPdfFileTypes = ['application/pdf']

export const collections = Object.freeze({
  users: {
    slug: 'users',
    roles: {
      subscriber: 'subscriber',
      editor: 'editor',
      admin: 'admin',
      superAdmin: 'super-admin'
    } as const
  },
  categories: {
    slug: 'categories'
  },
  pages: {
    slug: 'pages'
  },
  posts: {
    slug: 'posts'
  },
  media: {
    slug: 'media',
    fileTypes: [...allowedImageFileTypes, ...allowedPdfFileTypes],
    maxFileSize: 5 * 1024 * 1024, // 5MB,
    maxFileSizeMb: '5MB',
    uploadDir: 'uploads/media'
  },
  shared: {
    minLength: 1,
    maxLength: 60,
    passwordMinLength: 8
  },
  blocks: {
    archiveBlock: {
      slug: 'archive'
    },
    banner: {
      slug: 'banner'
    },
    callToAction: {
      slug: 'cta'
    },
    code: {
      slug: 'code'
    },
    content: {
      slug: 'content'
    },
    form: {
      slug: 'formBlock'
    },
    mediaBlock: {
      slug: 'mediaBlock'
    }
  },
  header: {
    slug: 'header'
  },
  footer: {
    slug: 'footer'
  }
})

export const payload = Object.freeze({
  authTokenId: 'payload-token',
  emailExistsError: 'A user with the given email is already registered.',
  credentialsError: 'The email or password provided is incorrect.',
  userLocked: 'This user is locked due to having too many failed login attempts.',
  notFound: 'Not Found',
  tokenExpired: 'Token is either invalid or has expired.'
})
